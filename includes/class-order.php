<?php
/**
 * Class Order file.
 *
 * @package WC_ShipStation
 */

namespace WooCommerce\Shipping\ShipStation;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use WP_Post;
use WC_Order;
use WooCommerce\Shipping\ShipStation\Order_Util;
use WooCommerce\Shipping\ShipStation\API\REST\Orders_Controller;

/**
 * Order Class
 */
class Order {
	use Order_Util;

	/**
	 * Class constructor.
	 */
	public function __construct() {
		add_action( 'add_meta_boxes', array( $this, 'order_meta_box' ), 10, 2 );
		add_action( 'admin_enqueue_scripts', array( $this, 'admin_enqueue_scripts_styles' ), 10 );
	}

	/**
	 * Enqueue admin styling script.
	 */
	public function admin_enqueue_scripts_styles() {
		$screen = get_current_screen();

		$order_screen_id = self::get_meta_box_screen();
		if ( $screen->id === $order_screen_id ) {
			wp_enqueue_style( 'shipstation-admin-order', plugins_url( 'assets/css/admin-order.css', WC_SHIPSTATION_FILE ), array(), WC_SHIPSTATION_VERSION );
		}
	}

	/**
	 * Add meta box in the order admin page.
	 *
	 * @param string           $post_type Type of the post.
	 * @param WP_Post|WC_Order $post_or_order_object Either WP_Post or WC_Order object.
	 */
	public function order_meta_box( $post_type, $post_or_order_object ) {
		if ( ! ( $post_or_order_object instanceof WP_Post ) && ! ( $post_or_order_object instanceof WC_Order ) ) {
			return;
		}

		$order = $post_or_order_object instanceof WP_Post ? wc_get_order( $post_or_order_object->ID ) : $post_or_order_object;

		if ( ! $order instanceof WC_Order ) {
			return;
		}

		$saved_notifications = $order->get_meta( Orders_Controller::$notification_meta_name );

		if ( ! empty( $saved_notifications ) && is_array( $saved_notifications ) ) {
			add_meta_box(
				'wc-shipstation-notifications',
				esc_html__( 'ShipStation Notifications', 'woocommerce-shipstation-integration' ),
				array( $this, 'packages_meta_box' ),
				self::get_meta_box_screen(),
				'normal',
				'core'
			);
		}
	}

	/**
	 * Display a shipping package inside order meta box.
	 *
	 * @param WC_Order|WP_Post $post_or_order_object Either post or order object.
	 */
	public function packages_meta_box( $post_or_order_object ) {
		$order = $post_or_order_object instanceof WP_Post ? wc_get_order( $post_or_order_object->ID ) : $post_or_order_object;

		if ( ! $order instanceof WC_Order ) {
			return;
		}

		$saved_notifications = $order->get_meta( Orders_Controller::$notification_meta_name );

		ob_start();
		?>
		<div class="wc-shipstation-notifications-wrapper">
			<div class="wc-shipstation-notifications-container">
				<div class="wc-shipstation-notifications">

				<?php foreach ( $saved_notifications as $notification ) { ?>

					<div class="wc-shipstation-notification">
						<?php if ( ! empty( $notification['notification_id'] ) ) { ?>
						<div class="notification-info notification-id">
							<span class="notification-label"><?php esc_html_e( 'Notification ID:', 'woocommerce-shipstation-notification' ); ?></span>
							<span class="notification-value"><?php echo esc_html( $notification['notification_id'] ); ?></span>
						</div>
						<?php } ?>

						<?php if ( ! empty( $notification['tracking_number'] ) ) { ?>
						<div class="notification-info  notification-tracking-number">
							<span class="notification-label"><?php esc_html_e( 'Tracking Number', 'woocommerce-shipstation-notification' ); ?></span>
							<span class="notification-value"><?php echo esc_html( $notification['tracking_number'] ); ?></span>
						</div>
						<?php } ?>

						<?php if ( ! empty( $notification['tracking_url'] ) ) { ?>
						<div class="notification-info  notification-tracking-url">
							<span class="notification-label"><?php esc_html_e( 'Tracking URL', 'woocommerce-shipstation-notification' ); ?></span>
							<span class="notification-value"><?php echo esc_html( $notification['tracking_url'] ); ?></span>
						</div>
						<?php } ?>

						<?php if ( ! empty( $notification['carrier_code'] ) ) { ?>
						<div class="notification-info  notification-carrier-code">
							<span class="notification-label"><?php esc_html_e( 'Carrier Code', 'woocommerce-shipstation-notification' ); ?></span>
							<span class="notification-value"><?php echo esc_html( $notification['carrier_code'] ); ?></span>
						</div>
						<?php } ?>

						<?php if ( ! empty( $notification['items'] ) && is_array( $notification['items'] ) ) { ?>
						<div class="notification-info notification-items">
							<span class="notification-label"><?php esc_html_e( 'Items:', 'woocommerce-shipstation-notification' ); ?></span>
							<?php foreach ( $notification['items'] as $item ) { ?>
								<?php
									$product_name = isset( $item['product_id'] ) ? wc_get_product( $item['product_id'] ) : false;
								?>
								<div class="notification-item-row">
									<div class="item-product">
										<div class="product-name">
											<?php if ( isset( $item['description'] ) ) { ?>
											<span class="description"><?php echo esc_html( $item['description'] ); ?></span>
											<?php } ?>

											<?php if ( isset( $item['sku'] ) ) { ?>
											<span class="sku"><?php echo esc_html( $item['sku'] ); ?></span>
											<?php } ?>
										</div>

										<?php if ( isset( $item['quantity'] ) ) { ?>
										<div class="product-qty">
											<span class="quantity"><?php echo esc_html( $item['quantity'] ); ?></span>
										</div>
										<?php } ?>
									</div>
								</div>
							<?php } ?>
						</div>
						<?php } ?>

						<?php if ( ! empty( $notification['ship_to'] ) && is_array( $notification['ship_to'] ) ) { ?>
						<div class="notification-info notification-ship-to">
							<span class="notification-label"><?php esc_html_e( 'Ship To:', 'woocommerce-shipstation-notification' ); ?></span>
							<div class="notification-value">
								<?php foreach ( $notification['ship_to'] as $key => $address_info ) { ?>
									<?php if ( ! empty( $address_info ) ) { ?>
										<span class="<?php echo esc_attr( $key ); ?>"><?php echo esc_html( $address_info ); ?>, </span>
									<?php } ?>
								<?php } ?>
							</div>
						</div>
						<?php } ?>

						<?php if ( ! empty( $notification['ship_from'] ) && is_array( $notification['ship_from'] ) ) { ?>
						<div class="notification-info notification-ship-from">
							<span class="notification-label"><?php esc_html_e( 'Ship From:', 'woocommerce-shipstation-notification' ); ?></span>
							<div class="notification-value">
								<?php foreach ( $notification['ship_from'] as $key => $address_info ) { ?>
									<?php if ( ! empty( $address_info ) ) { ?>
										<span class="<?php echo esc_attr( $key ); ?>"><?php echo esc_html( $address_info ); ?>, </span>
									<?php } ?>
								<?php } ?>
							</div>
						</div>
						<?php } ?>
					</div>

				<?php }// end foreach loop. ?>

				</div>
			</div>
		</div>
		<?php
	}
}

new Order();
